%com.example.habits9.HabitsApplication&com.example.habits9.data.HabitDatabase"com.example.habits9.data.HabitType+com.example.habits9.data.NumericalHabitType$com.example.habits9.ui.MainViewModel7com.example.habits9.ui.createhabit.CreateHabitViewModel7com.example.habits9.ui.createyesnohabit.FrequencyOption=com.example.habits9.ui.managesections.ManageSectionsViewModel1com.example.habits9.ui.settings.SettingsViewModel#com.example.uhabits_99.MainActivity&com.example.habits9.data.FrequencyType"com.example.habits9.data.DayOfWeek                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 
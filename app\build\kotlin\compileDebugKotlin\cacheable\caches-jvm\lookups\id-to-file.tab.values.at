/ Header Record For PersistentHashMapValueStorage; :app/src/main/java/com/example/habits9/HabitsApplication.kt9 8app/src/main/java/com/example/habits9/data/Completion.kt< ;app/src/main/java/com/example/habits9/data/CompletionDao.ktC Bapp/src/main/java/com/example/habits9/data/CompletionRepository.kt4 3app/src/main/java/com/example/habits9/data/Habit.kt7 6app/src/main/java/com/example/habits9/data/HabitDao.kt< ;app/src/main/java/com/example/habits9/data/HabitDatabase.kt> =app/src/main/java/com/example/habits9/data/HabitRepository.kt; :app/src/main/java/com/example/habits9/data/HabitSection.kt> =app/src/main/java/com/example/habits9/data/HabitSectionDao.ktE Dapp/src/main/java/com/example/habits9/data/HabitSectionRepository.kt8 7app/src/main/java/com/example/habits9/data/HabitType.ktH Gapp/src/main/java/com/example/habits9/data/UserPreferencesRepository.kt; :app/src/main/java/com/example/habits9/di/DatabaseModule.kt: 9app/src/main/java/com/example/habits9/ui/MainViewModel.ktM Lapp/src/main/java/com/example/habits9/ui/createhabit/CreateHabitViewModel.kt^ ]app/src/main/java/com/example/habits9/ui/createmeasurablehabit/CreateMeasurableHabitScreen.ktT Sapp/src/main/java/com/example/habits9/ui/createyesnohabit/CreateYesNoHabitScreen.ktG Fapp/src/main/java/com/example/habits9/ui/details/HabitDetailsScreen.ktX Wapp/src/main/java/com/example/habits9/ui/habittypeselection/HabitTypeSelectionScreen.kt< ;app/src/main/java/com/example/habits9/ui/home/<USER>/src/main/java/com/example/habits9/ui/managesections/ManageSectionsScreen.ktS Rapp/src/main/java/com/example/habits9/ui/managesections/ManageSectionsViewModel.ktD Capp/src/main/java/com/example/habits9/ui/settings/SettingsScreen.ktG Fapp/src/main/java/com/example/habits9/ui/settings/SettingsViewModel.kt9 8app/src/main/java/com/example/uhabits_99/MainActivity.kt; :app/src/main/java/com/example/uhabits_99/ui/theme/Color.kt; :app/src/main/java/com/example/uhabits_99/ui/theme/Theme.kt: 9app/src/main/java/com/example/uhabits_99/ui/theme/Type.kt: 9app/src/main/java/com/example/habits9/ui/MainViewModel.kt7 6app/src/main/java/com/example/habits9/data/HabitDao.kt> =app/src/main/java/com/example/habits9/data/HabitRepository.kt: 9app/src/main/java/com/example/habits9/ui/MainViewModel.ktL Kapp/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt< ;app/src/main/java/com/example/habits9/ui/home/<USER>/src/main/java/com/example/habits9/ui/MainViewModel.ktL Kapp/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt< ;app/src/main/java/com/example/habits9/ui/home/<USER>/src/main/java/com/example/habits9/ui/MainViewModel.kt< ;app/src/main/java/com/example/habits9/ui/home/<USER>/src/main/java/com/example/habits9/ui/MainViewModel.kt< ;app/src/main/java/com/example/habits9/ui/home/<USER>/src/main/java/com/example/habits9/data/FrequencyModels.kt4 3app/src/main/java/com/example/habits9/data/Habit.kt< ;app/src/main/java/com/example/habits9/data/HabitDatabase.kt; :app/src/main/java/com/example/habits9/di/DatabaseModule.ktU Tapp/src/main/java/com/example/habits9/ui/components/EnhancedFrequencyPickerDialog.ktM Lapp/src/main/java/com/example/habits9/ui/createhabit/CreateHabitViewModel.kt^ ]app/src/main/java/com/example/habits9/ui/createmeasurablehabit/CreateMeasurableHabitScreen.ktT Sapp/src/main/java/com/example/habits9/ui/createyesnohabit/CreateYesNoHabitScreen.kt9 8app/src/main/java/com/example/habits9/data/Completion.kt7 6app/src/main/java/com/example/habits9/data/HabitDao.kt> =app/src/main/java/com/example/habits9/data/HabitRepository.kt: 9app/src/main/java/com/example/habits9/ui/MainViewModel.kt< ;app/src/main/java/com/example/habits9/ui/home/<USER>/src/main/java/com/example/habits9/ui/MainViewModel.kt< ;app/src/main/java/com/example/habits9/ui/home/<USER>/src/main/java/com/example/habits9/utils/HabitScheduler.kt